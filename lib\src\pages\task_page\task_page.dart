import 'package:flutter/material.dart';
import 'package:tencent_chat_i18n_tool/tencent_chat_i18n_tool.dart';

class TaskPage extends StatefulWidget {
  const TaskPage({super.key});

  @override
  State<StatefulWidget> createState() => _TaskPageState();
}

class _TaskPageState extends State<TaskPage> {
  Map<int, Color> taskStatusButColors = {
    1: const Color(0xFFE4E4E4), // 对应颜色 1
    2: const Color(0xFFFF5B2D), // 对应颜色 2
    3: const Color(0xFFFFB61F), // 对应颜色 3
  };

  dynamic getTaskMap(Map<int, dynamic> taskStatusMap, int taskStatus) {
    return taskStatusMap[taskStatus];
  }

  Widget _buildShaderText({required Widget child}) {
    return ShaderMask(
        shaderCallback: (bounds) => const LinearGradient(
              colors: [Color(0xFFEE3637), Color(0xFFEEAB36)],
              begin: Alignment.topLeft,
              stops: [0.3, 1],
              end: Alignment.bottomRight,
            ).createShader(bounds),
        child: child);
  }

  Widget _buildTaskItem({
    required String taskNumber,
    required String taskDescription,
    //任务状态 1.已领取 2.已完成但没领取 3.未完成
    required int taskStatus,
    bool isNextImg = true,
    String? buttonText,
  }) {
    Map<int, Color> taskStatusLeftColors = {
      1: const Color(0xFFFF5B2D), // 对应颜色 1
      2: Colors.white.withOpacity(0.6), // 对应颜色 2
      3: Colors.white.withOpacity(0.6), // 对应颜色 3
    };
    Map<int, Color> taskStatusLeftTextColors = {
      1: Colors.white, // 对应颜色 1
      2: const Color(0xFFFF5B2D), // 对应颜色 2
      3: const Color(0xFFFF5B2D), // 对应颜色 3
    };
    Map<int, String> taskStatusButTexts = {
      1: TIM_t('已领取'),
      2: TIM_t('领取'),
      3: TIM_t('去完成'),
    };
    Map<int, String> taskStatusNextImgs = {
      1: 'assets/task_page/task_next_active.png',
      2: 'assets/task_page/task_next.png',
      3: 'assets/task_page/task_next.png',
    };
    Map<int, Color> taskStatusButTextColors = {
      1: const Color(0xFF666666), // 对应颜色 1
      2: const Color(0xFFFFFFFF), // 对应颜色 2
      3: const Color(0xFFFFFFFF), // 对应颜色 3
    };

    Color taskButColor = getTaskMap(taskStatusButColors, taskStatus);
    Color taskButTextColor = getTaskMap(taskStatusButTextColors, taskStatus);
    String taskButText = getTaskMap(taskStatusButTexts, taskStatus);
    Color taskStatusLeftColor = getTaskMap(taskStatusLeftColors, taskStatus);
    Color taskStatusLeftTextColor =
        getTaskMap(taskStatusLeftTextColors, taskStatus);
    String taskStatusNextImg = getTaskMap(taskStatusNextImgs, taskStatus);

    return Stack(clipBehavior: Clip.none, children: [
      Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          color: Colors.white.withOpacity(0.6),
        ),
        child: Row(
          children: [
            // 任务编号
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: taskStatusLeftColor,
                //边框样式
                border: Border.all(
                  color: const Color(0xFFFF5B2D),
                ),
                borderRadius: BorderRadius.circular(16),
              ),
              child: Text(
                taskNumber,
                style: TextStyle(
                  fontSize: 12,
                  color: taskStatusLeftTextColor,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),

            const SizedBox(width: 12),

            // 任务描述
            Expanded(
              child: Text(
                taskDescription,
                style: const TextStyle(
                  fontSize: 11,
                  color: Color(0xFF903D1F),
                  height: 1.4,
                ),
              ),
            ),
            const SizedBox(width: 12),
            TaskStatusButton(taskButText: taskButText, taskButColor: taskButColor, taskButTextColor: taskButTextColor),

          ],
        ),
      ),
      if (isNextImg)
        Positioned(left: 30, bottom: -18, child: Image.asset(taskStatusNextImg))
    ]);
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        const AppLogo(),

        SingleChildScrollView(
            child: Column(
          children: [
            SizedBox(height: 100),
            ShaderMask(
                shaderCallback: (bounds) => const LinearGradient(
                  colors: [Color(0xFFFFE786), Color(0xFFFFFFFF)],
                  begin: Alignment.topCenter,
                  stops: [0.3, 1],
                  end: Alignment.bottomCenter,
                ).createShader(bounds),
                child: Text(TIM_t('天天领红包'),
                    style: const TextStyle(
                      fontSize: 28,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ))),
            Text(TIM_t('做任务') + ' ' + TIM_t('领奖励'),
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                )),
            // Container(
            //     child: Row(
            //       mainAxisAlignment: MainAxisAlignment.center,
            //       children: [
            //         Container(
            //             width: 150,
            //             height: 61,
            //             alignment: Alignment.topCenter,
            //             //top
            //             padding: const EdgeInsets.only(top: 15),
            //             decoration: const BoxDecoration(
            //               image: DecorationImage(
            //                 image: AssetImage(
            //                     'assets/task_page/task_but_yellow.png'),
            //                 fit: BoxFit.fill,
            //               ),
            //             ),
            //             child: Text(TIM_t('立即邀请'),
            //                 style: TextStyle(
            //                   fontSize: 16,
            //                   color: Colors.white,
            //                 ))),
            //         const SizedBox(width: 20),
            //         Container(
            //             alignment: Alignment.topCenter,
            //             //top
            //             padding: const EdgeInsets.only(top: 15),
            //             width: 150,
            //             height: 61,
            //             decoration: const BoxDecoration(
            //               image: DecorationImage(
            //                 image:
            //                     AssetImage('assets/task_page/task_but_red.png'),
            //                 fit: BoxFit.fill,
            //               ),
            //             ),
            //             child: Text(TIM_t('面对面邀请'),
            //                 style: TextStyle(
            //                   fontSize: 16,
            //                   color: Colors.white,
            //                 ))),
            //       ],
            //     ),
            //     margin: EdgeInsets.only(top: 276)),
            Container(
              height: 38,
              margin: const EdgeInsets.only(top: 156),
              padding: const EdgeInsets.symmetric(horizontal: 32),
              child: TextField(
                textAlign: TextAlign.center, // 文本居中
                decoration: InputDecoration(
                  fillColor: const Color(0xFFD4514D), 
                  // 设置背景颜色
                  filled: true,
                  // 启用背景颜色
                  hintText: '输入好友验证码',
                  // 提示文字
                  contentPadding:
                      EdgeInsets.symmetric(vertical: 0, horizontal: 0),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8), // 圆角
                    borderSide: BorderSide.none, // 去除边框
                  ),
                  hintStyle: const TextStyle(color: Color(0xFF903D1F)),
                  // 提示文字样式
                  suffixIcon: Padding(
                    padding: EdgeInsets.only(right: 16),
                    child: Image.asset(
                      width: 16,
                      height: 16,
                      'assets/icon_edit.png',
                    ),
                  ),
                  suffixIconConstraints: const BoxConstraints(
                    minWidth: 16,
                    minHeight: 16,
                  ),
                ),
              ),
            ),
            SizedBox(height: 8),
            Stack(
              clipBehavior: Clip.none,
              children: [
                Container(
                  margin: const EdgeInsets.symmetric(horizontal: 16),
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(20),
                    image: const DecorationImage(
                      image: AssetImage('assets/task_page/task_card_bg.png'),
                      // Use AssetImage instead of Image.asset
                      fit: BoxFit.fill, // Optional, controls how the image fits
                    ),
                  ),
                  child: Column(
                    children: [
                      // 顶部标题栏
                      Column(
                        children: [
                          Container(
                              padding: const EdgeInsets.symmetric(vertical: 6),
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(16),
                                gradient: const LinearGradient(
                                  colors: [
                                    Color(0xFFFFDFD5),
                                    Color(0x00FDE8F2)
                                  ],
                                  begin: Alignment.topLeft,
                                  end: Alignment.bottomRight,
                                ),
                              ),
                              child: Stack(
                                clipBehavior: Clip.none,
                                children: [
                                  Row(
                                    children: [
                                      const SizedBox(width: 48),
                                      _buildShaderText(
                                          child: Text(
                                        TIM_t('我的邀请事业'),
                                        style: TextStyle(
                                          fontSize: 14,
                                          fontWeight: FontWeight.bold,
                                          color: Colors.white,
                                        ),
                                      )),

                                      // const SizedBox(width: 100),
                                      const Spacer(),
                                      Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          Text(
                                            TIM_t('任务规则'),
                                            style: TextStyle(
                                              fontSize: 14,
                                              color: Color(0xFFFD8888),
                                            ),
                                          ),
                                          SizedBox(width: 4),
                                          Icon(
                                            Icons.chevron_right,
                                            color: Color(0xFFFD8888),
                                            size: 16,
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                  Positioned(
                                      left: 0,
                                      bottom: -8,
                                      child: Container(
                                        child: Image.asset(
                                            width: 42,
                                            height: 42,
                                            'assets/task_page/task_red.png'),
                                      )),
                                ],
                              )),
                          //我的邀请码
                        ],
                      ),
                      const SizedBox(height: 20),
                    ],
                  ),
                ),
                // Positioned(
                //   left: 0,
                //   right: 0,
                //   bottom: -20,
                //     child: Image.asset("assets/task_page/corner.png"))
              ],
            ),

            SizedBox(height: 8),

            Stack(
              clipBehavior: Clip.none,
              children: [
                Container(
                  margin: const EdgeInsets.symmetric(horizontal: 16),
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(20),
                    image: const DecorationImage(
                      image: AssetImage('assets/task_page/task_card_bg.png'),
                      // Use AssetImage instead of Image.asset
                      fit: BoxFit.fill, // Optional, controls how the image fits
                    ),
                  ),
                  child: Column(
                    children: [
                      // 顶部标题栏
                      Column(
                        children: [
                          Container(
                              padding: const EdgeInsets.symmetric(vertical: 6),
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(16),
                                gradient: const LinearGradient(
                                  colors: [
                                    Color(0xFFFFDFD5),
                                    Color(0x00FDE8F2)
                                  ],
                                  begin: Alignment.topLeft,
                                  end: Alignment.bottomRight,
                                ),
                              ),
                              child: Stack(
                                clipBehavior: Clip.none,
                                children: [
                                  Row(
                                    children: [
                                      const SizedBox(width: 48),
                                      _buildShaderText(
                                          child: Text(
                                        TIM_t('新手任务'),
                                        style: TextStyle(
                                          fontSize: 14,
                                          fontWeight: FontWeight.bold,
                                          color: Colors.white,
                                        ),
                                      )),

                                      // const SizedBox(width: 100),
                                      const Spacer(),
                                      Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          Text(
                                            TIM_t('任务规则'),
                                            style: TextStyle(
                                              fontSize: 14,
                                              color: Color(0xFFFD8888),
                                            ),
                                          ),
                                          SizedBox(width: 4),
                                          Icon(
                                            Icons.chevron_right,
                                            color: Color(0xFFFD8888),
                                            size: 16,
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                  Positioned(
                                      left: 0,
                                      bottom: -8,
                                      child: Container(
                                        child: Image.asset(
                                            width: 42,
                                            height: 42,
                                            'assets/task_page/task_red.png'),
                                      )),
                                ],
                              )),
                        ],
                      ),
                      const SizedBox(height: 20),
                      // 任务一
                      _buildTaskItem(
                        taskNumber: '任务一',
                        taskDescription: '注册登录后领取',
                        taskStatus: 1,
                      ),
                      const SizedBox(height: 16),
                      // 任务二
                      _buildTaskItem(
                        taskNumber: '任务二',
                        taskDescription: '转发红包任务页面分享至Facebook和Instagram',
                        taskStatus: 1,
                      ),
                      const SizedBox(height: 16),
                      // 任务三
                      _buildTaskItem(
                        taskNumber: '任务三',
                        taskDescription: '邀请1名新用户注册',
                        taskStatus: 2,
                      ),
                      const SizedBox(height: 16),

                      _buildTaskItem(
                        taskNumber: '任务四',
                        taskDescription: '邀请 3 名好友组建群聊',
                        taskStatus: 3,
                      ),
                      const SizedBox(height: 16),

                      _buildTaskItem(
                          taskNumber: '任务五',
                          taskDescription: '充值 ₱10 并发红包到聊天群',
                          taskStatus: 3,
                          isNextImg: false),

                      const SizedBox(height: 24),

                      // 底部奖励说明
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(16),
                          gradient: const LinearGradient(
                            colors: [Color(0xFFFFDFD5), Color(0x00FDE8F2)],
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                          ),
                        ),
                        child: Column(
                          children: [
                            Row(
                              children: [
                                _buildShaderText(
                                    child: Text(
                                  TIM_t('完成任务'),
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: Colors.white,
                                    fontWeight: FontWeight.bold,
                                  ),
                                )),
                                const SizedBox(width: 8),
                                Expanded(
                                    child: Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      '完成任务一至四：',
                                      style: TextStyle(
                                        fontSize: 11,
                                        color: Color(0xFF2D3748),
                                      ),
                                    ),
                                    Text(
                                      TIM_t('最高可得'),
                                      style: TextStyle(
                                        fontSize: 11,
                                        color: Color(0xFF2D3748),
                                      ),
                                    ),
                                  ],
                                )),
                                Image.asset(
                                    'assets/task_page/task_next_right.png'),
                                Image.asset('assets/task_page/gold.png'),
                                Text(
                                  '₱20',
                                  style: TextStyle(
                                    fontSize: 13,
                                    color: Color(0xFFE53E3E),
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            Row(
                              children: [
                                _buildShaderText(
                                    child: Text(
                                  TIM_t('领取任务'),
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: Colors.white,
                                    fontWeight: FontWeight.bold,
                                  ),
                                )),
                                const SizedBox(width: 8),
                                Expanded(
                                    child: Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      '完成任务五：',
                                      style: TextStyle(
                                        fontSize: 11,
                                        color: Color(0xFF2D3748),
                                      ),
                                    ),
                                    Text(
                                      TIM_t('最高可得'),
                                      style: TextStyle(
                                        fontSize: 11,
                                        color: Color(0xFF2D3748),
                                      ),
                                    ),
                                  ],
                                )),
                                Image.asset(
                                    'assets/task_page/task_next_right.png'),
                                Image.asset('assets/task_page/gold.png'),
                                Text(
                                  '₱20',
                                  style: TextStyle(
                                    fontSize: 13,
                                    color: Color(0xFFE53E3E),
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                // Positioned(
                //   left: 0,
                //   right: 0,
                //   bottom: -20,
                //     child: Image.asset("assets/task_page/corner.png"))
              ],
            ),
            const SizedBox(height: 10),
            // Stack(
            //   children: [
            //     Container(
            //   margin: const EdgeInsets.symmetric(horizontal: 16),
            //   padding: const EdgeInsets.all(16),
            //   decoration: BoxDecoration(
            //     borderRadius: BorderRadius.circular(20),
            //     image: const DecorationImage(
            //       image: AssetImage('assets/task_page/task_card_bg.png'),
            //       // Use AssetImage instead of Image.asset
            //       fit: BoxFit.fill, // Optional, controls how the image fits
            //     ),
            //   ),
            //   child: Column(
            //     children: [
            //       // 顶部标题栏
            //       Column(
            //         children: [
            //           Container(
            //               padding: const EdgeInsets.symmetric(vertical: 6),
            //               decoration: BoxDecoration(
            //                 borderRadius: BorderRadius.circular(16),
            //                 gradient: const LinearGradient(
            //                   colors: [Color(0xFFFFDFD5), Color(0x00FDE8F2)],
            //                   begin: Alignment.topLeft,
            //                   end: Alignment.bottomRight,
            //                 ),
            //               ),
            //               child: Stack(
            //                 clipBehavior: Clip.none,
            //                 children: [
            //                   Row(
            //                     children: [
            //                       const SizedBox(width: 48),
            //                       _buildShaderText(
            //                           child: const Text(
            //                             '邀请好友',
            //                             style: TextStyle(
            //                               fontSize: 18,
            //                               fontWeight: FontWeight.bold,
            //                               color: Colors.white,
            //                             ),
            //                           )),
            //
            //                       // const SizedBox(width: 100),
            //                       const Spacer(),
            //                       const Row(
            //                         mainAxisSize: MainAxisSize.min,
            //                         children: [
            //                           Text(
            //                             '签到规则',
            //                             style: TextStyle(
            //                               fontSize: 14,
            //                               color: Color(0xFFFD8888),
            //                             ),
            //                           ),
            //                           SizedBox(width: 4),
            //                           Icon(
            //                             Icons.chevron_right,
            //                             color: Color(0xFFFD8888),
            //                             size: 16,
            //                           ),
            //                         ],
            //                       ),
            //                     ],
            //                   ),
            //                   Positioned(
            //                       left: 0,
            //                       bottom: -12,
            //                       child: Container(
            //                         child: Image.asset(
            //                             width: 44,
            //                             height: 44,
            //                             fit: BoxFit.fill,
            //                             'assets/task_page/love.png'),
            //                       )),
            //                 ],
            //               )),
            //         ],
            //       ),
            //
            //     ],
            //   ),
            // )
            // ],),
            // Container( child: Text('123123'))
          ],
        )),
        Positioned(
          top: MediaQuery.of(context).padding.top, // 状态栏高度 + 10px
          left: 0,
          right: 0,
          child: Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    SizedBox(height: 10),
                    Row(
                      children: [
                        IconButton(
                          color: Colors.white,
                          icon: const Icon(Icons.arrow_back_ios_new_outlined),
                          onPressed: () {
                            print("object");
                            Navigator.of(context).pop();
                          },
                        ),
                        SizedBox(width: 20),
                        TaskInfoButton(
                          amount: '10',
                          activatedCount: '50',
                        ),
                      ],
                    ),

                  ],
                ),
              )
            ],
          ),
        ),

      ],
    );
  }

// 辅助方法：构建任务项
}

class TaskStatusButton extends StatelessWidget {
  final String taskButText;

  final Color taskButColor;

  final Color taskButTextColor;

  const TaskStatusButton({
    super.key,
    required this.taskButText,
    required this.taskButColor,
    required this.taskButTextColor,
  });

  @override
  Widget build(BuildContext context) {
    // 状态按钮
    return Container(
      width: 55,
      padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 8),
      alignment: Alignment.center,
      decoration: BoxDecoration(
        color: taskButColor,
        borderRadius: BorderRadius.circular(50),
      ),
      child: Text(
        taskButText,
        style: TextStyle(
          fontSize: 12,
          color: taskButTextColor,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }
}

class TaskInfoButton extends StatelessWidget {
  final String amount;
  final String activatedCount;

  const TaskInfoButton({
    Key? key,
    required this.amount,
    required this.activatedCount,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 30),
      decoration: const BoxDecoration(
        image: DecorationImage(
          image: AssetImage('assets/task_page/info_but.png'),
          fit: BoxFit.fill,
        ),
      ),
      child: Row(
        children: [
          // 已获得
          Row(
            children: [
              Text(
                TIM_t('已获得') + ' ',
                style: TextStyle(
                  color: Color(0xFF855D1D),
                  fontSize: 14,
                ),
              ),
              Text(
                '₱$amount',
                style: const TextStyle(
                  color: Color(0xffE14D2A),
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(width: 36),
          // 已激活
          Row(
            children: [
              Text(
                TIM_t('已激活') + ' ',
                style: TextStyle(
                  color: Color(0xFF855D1D),
                  fontSize: 14,
                ),
              ),
              Text(
                activatedCount,
                style: const TextStyle(
                  color: Color(0xffE14D2A),
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

class AppLogo extends StatelessWidget {
  const AppLogo({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final double height = MediaQuery.of(context).size.height;
    final double width = MediaQuery.of(context).size.width;
    return Stack(
      children: [
        Container(
          child: Image.asset(
            'assets/task_page/task_page_bg.png',
            width: width,
            height: height,
            fit: BoxFit.cover,
          ),
        ),
      ],
    );
  }
}
